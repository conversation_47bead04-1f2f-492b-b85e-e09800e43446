/** @odoo-module **/

import publicWidget from "@web/legacy/js/public/public_widget";

publicWidget.registry.EventDepositWidget = publicWidget.Widget.extend({
    selector: '#modal_ticket_registration',
    events: {
        'input .initial-deposit-input': '_onDepositChange',
        'change select[name^="nb_register-"]': '_onTicketQuantityChange',
        'submit #registration_form': '_onFormSubmit',
    },

    start: function () {
        this._super.apply(this, arguments);
        this._updateTotalPrice();
    },

    _onDepositChange: function (ev) {
        this._updateTotalPrice();
    },

    _onTicketQuantityChange: function (ev) {
        this._updateTotalPrice();
    },

    _onFormSubmit: function (ev) {
        // Get all deposit inputs and add them to the form data
        const form = ev.currentTarget;
        const depositInputs = this.$('.initial-deposit-input');

        depositInputs.each(function() {
            const depositInput = $(this);
            const depositAmount = parseFloat(depositInput.val()) || 0;
            const ticketId = depositInput.data('ticket-id');

            if (depositAmount > 0 && ticketId) {
                // Create hidden input for deposit amount for this specific ticket
                const depositAmountInput = document.createElement('input');
                depositAmountInput.type = 'hidden';
                depositAmountInput.name = `deposit_amount_${ticketId}`;
                depositAmountInput.value = depositAmount;
                form.appendChild(depositAmountInput);

                // Create hidden input for deposit type (always USD for events)
                const depositTypeInput = document.createElement('input');
                depositTypeInput.type = 'hidden';
                depositTypeInput.name = `deposit_type_${ticketId}`;
                depositTypeInput.value = 'usd';
                form.appendChild(depositTypeInput);
            }
        });
    },

    _updateTotalPrice: function () {
        let totalDepositAmount = 0;
        let totalTicketPrice = 0;

        // Calculate total deposit amount
        this.$('.initial-deposit-input').each(function() {
            const depositAmount = parseFloat($(this).val()) || 0;
            totalDepositAmount += depositAmount;
        });

        // Calculate total ticket price based on selected quantities
        this.$('select[name^="nb_register-"]').each(function () {
            const select = $(this);
            const quantity = parseInt(select.val()) || 0;
            const ticketContainer = select.closest('.o_wevent_ticket_selector, .o_wevent_registration_single');

            // Try to find price information (this might need adjustment based on actual DOM structure)
            let ticketPrice = 0;
            const priceElement = ticketContainer.find('[data-price]');
            if (priceElement.length) {
                ticketPrice = parseFloat(priceElement.attr('data-price')) || 0;
            }

            totalTicketPrice += quantity * ticketPrice;
        });

        // Update display if there's a price display element
        const priceDisplay = this.$('.o_wevent_price_range, .o_wevent_total_price');
        if (priceDisplay.length && totalTicketPrice > 0) {
            const finalPrice = Math.max(0, totalTicketPrice - totalDepositAmount);
            const depositText = totalDepositAmount > 0 ? ` (Total Deposit: $${totalDepositAmount.toFixed(2)})` : '';
            priceDisplay.text(`Total: $${finalPrice.toFixed(2)}${depositText}`);
        }
    },
});
