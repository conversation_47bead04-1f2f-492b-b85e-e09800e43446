# -*- coding: utf-8 -*-

import logging
from collections import defaultdict
from odoo.http import request, route

from odoo.addons.website_event_sale.controllers.main import WebsiteEventSaleController

_logger = logging.getLogger(__name__)


class WebsiteEventDepositController(WebsiteEventSaleController):

    @route()
    def registration_new(self, event, **post):
        """Override to capture deposit data from the initial form submission"""
        # Check if we have deposit information for any tickets
        deposit_data = {}

        for key, value in post.items():
            if key.startswith('deposit_amount_') and float(value or 0) > 0:
                ticket_id = key.replace('deposit_amount_', '')
                deposit_type_key = f'deposit_type_{ticket_id}'
                deposit_data[ticket_id] = {
                    'amount': float(value),
                    'type': post.get(deposit_type_key, 'usd')
                }

        if deposit_data:
            # Store deposit info in session for later use
            request.session['event_deposit_data'] = deposit_data
            request.session['event_deposit_event_id'] = event.id
            _logger.info("Stored deposit data in session for event %s: %s", event.id, deposit_data)

        # Call parent method to handle normal flow
        return super().registration_new(event, **post)

    def _create_attendees_from_registration_post(self, event, registration_data):
        """Override to handle initial deposit for event tickets"""
        # Check if we have deposit information in session
        deposit_data = request.session.get('event_deposit_data', {})
        deposit_event_id = request.session.get('event_deposit_event_id')

        # Only apply deposit if it's for this specific event
        if deposit_event_id != event.id:
            deposit_data = {}

        _logger.info("Event registration with deposit data: %s", deposit_data)

        # Call parent method to handle normal registration flow
        result = super()._create_attendees_from_registration_post(event, registration_data)

        # If we have deposit data and a sale order was created, update the order lines
        if deposit_data:
            order_sudo = request.website.sale_get_order()
            if order_sudo and order_sudo.id:
                self._apply_deposit_to_order_lines(order_sudo, deposit_data, event)

        # Clean up session data
        request.session.pop('event_deposit_data', None)
        request.session.pop('event_deposit_event_id', None)

        return result

    def _apply_deposit_to_order_lines(self, order_sudo, deposit_data, event):
        """Apply deposit to event ticket order lines - simplified to work with main subscription module"""
        # Find order lines related to event tickets that were just added for this specific event
        event_lines = order_sudo.order_line.filtered(
            lambda line: line.event_ticket_id and
                        line.event_ticket_id.event_id.id == event.id and
                        line.product_id.recurring_invoice
        )

        if not event_lines:
            _logger.info("No recurring event ticket lines found for deposit application")
            return

        # Apply deposit information to each line based on ticket ID
        for line in event_lines:
            ticket_id_str = str(line.event_ticket_id.id)
            if ticket_id_str in deposit_data:
                deposit_info = deposit_data[ticket_id_str]
                # Set deposit information on the line
                line.write({
                    'subscription_deposit_type': deposit_info['type'],
                    'subscription_deposit_value': deposit_info['amount'],
                })

                _logger.info(
                    "Set deposit info on event ticket line %s (ticket %s): %s %s",
                    line.product_id.name, ticket_id_str, deposit_info['amount'], deposit_info['type']
                )
