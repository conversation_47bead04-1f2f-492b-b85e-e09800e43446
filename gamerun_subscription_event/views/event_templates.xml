<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Inherit the modal_ticket_registration template to add initial deposit field for each ticket -->
        <template id="modal_ticket_registration_deposit" name="Modal for tickets registration with deposit" inherit_id="website_event.modal_ticket_registration">
            <!-- Add initial deposit field for each ticket in multi-ticket layout -->
            <xpath expr="//div[@class='d-flex flex-column flex-md-row align-items-center justify-content-between gap-2']/div[@class='ms-auto']" position="before">
                <t t-if="ticket.initial_deposit_value">
                    <div class="d-flex align-items-center me-3">
                        <label t-attf-for="initial_deposit_#{ticket.id}" class="form-label me-2 mb-0">Deposit ($):</label>
                        <input type="number"
                               class="form-control initial-deposit-input"
                               t-attf-id="initial_deposit_#{ticket.id}"
                               t-attf-name="initial_deposit_#{ticket.id}"
                               t-att-data-ticket-id="ticket.id"
                               min="0"
                               step="0.01"
                               placeholder="0.00"
                               style="width: 100px;"/>
                    </div>
                </t>
            </xpath>

            <!-- Add initial deposit field for single-ticket layout -->
            <xpath expr="//div[@class='o_wevent_registration_single']//div[@class='o_wevent_registration_single_select w-auto ms-auto']" position="before">
                <t t-if="ticket.initial_deposit_value">
                    <div class="d-flex align-items-center me-3">
                        <label for="initial_deposit_single" class="form-label me-2 mb-0">Deposit ($):</label>
                        <input type="number"
                               class="form-control initial-deposit-input"
                               id="initial_deposit_single"
                               name="initial_deposit_single"
                               t-att-data-ticket-id="tickets.id if tickets else 0"
                               min="0"
                               step="0.01"
                               placeholder="0.00"
                               style="width: 100px;"/>
                    </div>
                </t>
            </xpath>
        </template>
    </data>
</odoo>
