<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Inherit the modal_ticket_registration template to add initial deposit field for each ticket -->
        <template id="modal_ticket_registration_deposit" name="Modal for tickets registration with deposit" inherit_id="website_event.modal_ticket_registration">
            <!-- Add initial deposit field after ticket description in multi-ticket layout -->
            <xpath expr="//div[contains(@class,'o_wevent_ticket_selector')]//div[@itemscope='itemscope']" position="after">
                <div class="mt-2">
                    <label t-attf-for="initial_deposit_#{ticket.id}" class="form-label">Initial deposit:</label>
                    <br/>
                    <input type="number"
                           class="form-control initial-deposit-input"
                           t-attf-id="initial_deposit_#{ticket.id}"
                           t-attf-name="initial_deposit_#{ticket.id}"
                           t-att-data-ticket-id="ticket.id"
                           min="0"
                           step="0.01"
                           placeholder="0.00"
                           style="width: 150px;"/>
                </div>
            </xpath>

            <!-- Add initial deposit field after ticket description in single-ticket layout -->
            <xpath expr="//div[@class='o_wevent_registration_single']//div[@class='col-12 col-md-8 p-0']" position="after">
                <div class="col-12 mt-2">
                    <label for="initial_deposit_single" class="form-label">Initial deposit:</label>
                    <br/>
                    <input type="number"
                           class="form-control initial-deposit-input"
                           id="initial_deposit_single"
                           name="initial_deposit_single"
                           t-att-data-ticket-id="tickets.id if tickets else 0"
                           min="0"
                           step="0.01"
                           placeholder="0.00"
                           style="width: 150px;"/>
                </div>
            </xpath>
        </template>
    </data>
</odoo>
