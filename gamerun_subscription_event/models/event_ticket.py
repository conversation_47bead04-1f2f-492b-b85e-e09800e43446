# -*- coding: utf-8 -*-

import logging
from odoo import models, fields

_logger = logging.getLogger(__name__)


class EventTicket(models.Model):
    _inherit = 'event.event.ticket'

    allow_initial_deposit = fields.Boolean("Allow initial deposit", related='product_id.allow_initial_deposit',
                                           readonly=True, store=True, index=True)
    initial_deposit_type = fields.Selection(related='product_id.initial_deposit_type', readonly=True, store=True, index=True)
    initial_deposit_value = fields.Float(string="Deposit Value")

