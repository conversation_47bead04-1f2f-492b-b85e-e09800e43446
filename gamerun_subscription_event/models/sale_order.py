# -*- coding: utf-8 -*-

import logging
from odoo import models, api

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _cart_update_order_line(self, product_id, quantity, order_line, **kwargs):
        """Override to handle event ticket deposits specifically"""
        # Only process if this is an event ticket
        if kwargs.get('event_ticket_id'):
            product = self.env['product.product'].browse(product_id)
            if product.recurring_invoice:
                _logger.info(
                    "Processing event ticket for product %s (ticket ID: %s)",
                    product.name, kwargs.get('event_ticket_id')
                )

        return super()._cart_update_order_line(product_id, quantity, order_line, **kwargs)
