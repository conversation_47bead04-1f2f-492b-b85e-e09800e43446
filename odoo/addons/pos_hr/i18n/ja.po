# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_hr_multi_employee_sales_report
msgid "A collection of single session reports. One for each employee"
msgstr "一連の単発セッションレポート。1従業員につき1つ"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.single_employee_sales_report
msgid "Abigal Peterson"
msgstr "Abigal Peterson"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/product_screen/order_summary/order_summary.js:0
msgid "Access Denied"
msgstr "アクセスが拒否されました。"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_daily_sales_reports_wizard__add_report_per_employee
msgid "Add a report per each employee"
msgstr "1従業員ごとに1レポートを追加"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Advanced rights"
msgstr "高度な権利"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "All Employees"
msgstr "全従業員"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "銀行取引明細書内容"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Basic rights"
msgstr "基本的権利"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.view_pos_daily_sales_reports_wizard
msgid ""
"Can't generate a report per employee! as selected session has no orders "
"associated with any employee."
msgstr "従業員ごとのレポートを生成できません! 選択されたセッションには、どの従業員にも関連付けられたオーダがありません。"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Cash in/out"
msgstr "現金入金/出金"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_pos_payment__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_pos_session__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "キャッシャー"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/pos_order.py:0
msgid "Cashier %s"
msgstr "キャッシャー%s"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
msgid "Cashier name"
msgstr "キャッシャー名"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "Change Cashier"
msgstr "キャッシャーを変更"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/pos_session.py:0
msgid "Closed Register"
msgstr "レジ締め完了"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Counted"
msgstr "棚卸数量"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Difference"
msgstr "差異"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/login_screen/login_screen.js:0
msgid "Discard"
msgstr "破棄"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_account_bank_statement_line__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_pos_daily_sales_reports_wizard__employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "従業員"

#. module: pos_hr
#: model:ir.actions.report,name:pos_hr.multi_employee_sales_report_action
msgid "Employee Sales Details"
msgstr "従業員販売詳細"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.single_employee_sales_report
msgid "Employee Sales Report"
msgstr "従業員販売レポート"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.single_employee_sales_report
msgid "Employee:"
msgstr "従業員:"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
msgid "Employee: %(employee)s - PoS Config(s): %(config_list)s \n"
msgstr "従業員: %(employee)s - PoSコンフィグ: %(config_list)s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "Employees with basic access"
msgstr "基本的アクセス権のある従業員"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "Employees with manager access"
msgstr "管理者アクセス権のある従業員"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/login_screen/login_screen.xml:0
msgid "Enter your PIN"
msgstr "PINを入力して下さい"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "If left empty, all employees can log in to PoS"
msgstr "空の場合は、全ての従業員がPOSにログインできます。"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "If left empty, only Odoo users have extended rights in PoS"
msgstr "空の場合、OdooユーザのみがPOSの拡張権限を持ちます。"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/navbar/navbar.xml:0
msgid "Lock"
msgstr "ロック"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "No Cashiers"
msgstr "レジなし"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/login_screen/login_screen.xml:0
msgid "Open Register"
msgstr "レジをオープン"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/pos_session.py:0
msgid "Opened register"
msgstr "オープン済レジ"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Opening"
msgstr "オープニング"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/pos_session.py:0
msgid "Others"
msgstr "その他"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "PIN not found"
msgstr "PINが見つかりません"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "Password?"
msgstr "パスワード？"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.xml:0
msgid "Payments"
msgstr "支払"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/closing_popup/closing_popup.js:0
msgid "Payments in %(paymentMethod)s"
msgstr "支払: %(paymentMethod)s"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS設定"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr "POS日報"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "POSオーダ"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "POS注文レポート"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_payment
msgid "Point of Sale Payments"
msgstr "POS支払い"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_session
msgid "Point of Sale Session"
msgstr "POSセッション"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/print_report_button/print_report_button.xml:0
msgid "Print"
msgstr "印刷"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_product_product
msgid "Product Variant"
msgstr "プロダクトバリアント"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Select Employee(s)"
msgstr "従業員を選択"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_hr_single_employee_sales_report
msgid "Session sales details for a single employee"
msgstr "1従業員用のセッション販売詳細"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_session__employee_id
msgid "The employee who currently uses the cash register"
msgstr "現在レジを使用している従業員"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_account_bank_statement_line__employee_id
msgid "The employee who made the cash move."
msgstr "入出金処理を行った従業員。"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,help:pos_hr.field_pos_payment__employee_id
msgid "The employee who uses the cash register."
msgstr "レジを使用する従業員。"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "There is no cashier available."
msgstr "利用可能なキャッシャーはありません。"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/login_screen/login_screen.xml:0
msgid "Unlock Register"
msgstr "レジ解除"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
msgid "Wrong PIN"
msgstr "不正な暗証番号"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/screens/product_screen/order_summary/order_summary.js:0
msgid "You are not allowed to change the price of a product."
msgstr "プロダクト価格を変える権利はありません。"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr "有効なPOSセッションで使用されている従業員を削除することはできません。最初にセッションをクローズして下さい:\n"
