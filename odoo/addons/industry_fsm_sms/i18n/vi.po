# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sms
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_fsm_sms
#: model:sms.template,name:industry_fsm_sms.sms_template_data_fsm_intervention
msgid "Field Service: Intervention Scheduled"
msgstr "Dịch vụ hiện trường: <PERSON><PERSON> lên lịch cuộc <PERSON>ử lý"

#. module: industry_fsm_sms
#: model:sms.template,name:industry_fsm_sms.sms_template_data_fsm_onway
msgid "Field Service: Our team is on its way to your location."
msgstr "Dịch vụ thực địa: Chúng tôi đang trên đường tới vị trí của bạn."

#. module: industry_fsm_sms
#: model:sms.template,body:industry_fsm_sms.sms_template_data_fsm_onway
msgid "{{ object.company_id.name }}: We are on our way to your intervention."
msgstr ""
"{{ object.company_id.name }}: Chúng tôi đang trên đường đến xử lý tận nơi."

#. module: industry_fsm_sms
#: model:sms.template,body:industry_fsm_sms.sms_template_data_fsm_intervention
msgid ""
"{{ object.company_id.name }}: Your intervention is scheduled {{ 'from the ' "
"+ format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.date_deadline, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) if object.planned_date_begin and "
"object.date_deadline else '' }}"
msgstr ""
"{{ object.company_id.name }}: Cuộc xử lý của bạn đã được lên lịch vào {{ "
"'from the ' + format_datetime(object.planned_date_begin, "
"tz=object.partner_id.tz, lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.date_deadline, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) if object.planned_date_begin and "
"object.date_deadline else '' }}"
