<?xml version="1.0" encoding="utf-8"?>
<odoo>


<template id="event_details" name="Event Header">
    <t t-call="website_event.layout">
        <div class="h-100" name="event" itemscope="itemscope" itemtype="http://schema.org/Event">
            <meta itemprop="startDate" t-attf-content="{{event.date_begin}}Z"/>
            <meta itemprop="endDate" t-attf-content="{{event.date_end}}Z"/>
            <t t-out="0"/>
        </div>
    </t>
</template>

<!-- Event - Description -->
<template id="event_description_full" name="Event Description" track="1">
    <t t-set="hide_submenu" t-value="True"/>
    <t t-set="hide_register_cta" t-value="True"/>
    <t t-set="opt_event_description_cover_hidden" t-value="is_view_active('website_event.opt_event_description_cover_hidden')"/>
    <t t-set="opt_event_description_cover_top" t-value="is_view_active('website_event.opt_event_description_cover_top')"/>
    <t t-set="edition_cover_only" t-value="opt_event_description_cover_hidden and request.env.user.has_group('event.group_event_manager')"/>

    <t t-call="website_event.event_details">
        <div t-if="toast_message" class="o_wevent_register_toaster d-none" t-att-data-message="toast_message"/>

        <t t-if="opt_event_description_cover_top" t-call="website.record_cover">
            <t t-set="_record" t-value="event"/>
            <t t-set="use_filters" t-value="True"/>
            <t t-set="use_size" t-value="True"/>
            <t t-set="use_text_align" t-value="True"/>
            <t t-set="additionnal_classes" t-value="'o_wevent_event_main_cover o_wevent_event_main_cover_top overflow-hidden h-auto'"/>

            <div class="container d-flex flex-grow-1 h-100 w-100">
                <div id="o_wevent_event_main_cover_content" class="d-flex flex-column align-items-baseline w-100 py-3">
                    <span t-if="event.is_participating" class="badge text-bg-success o_wevent_badge">
                        <i class="fa fa-check me-2" role="img"/>Registered
                    </span>
                    <div class="w-100 mt-auto">
                        <h2 t-field="event.name" itemprop="name" placeholder="Event Title" class="mt-3"/>
                        <p class="lead" t-field="event.subtitle" placeholder="Event Subtitle"/>
                    </div>
                </div>
            </div>
        </t>

        <section id="o_wevent_event_main" class="h-100" t-cache="event if not editable and event.website_published else None">
            <div class="container overflow-hidden pb-5">
                <div class="row">
                    <div class="col pe-xxl-5">
                        <t t-call="website_event.navbar"/>
                        <!-- Show the button tickets only on mobile -->
                        <div class="o_wevent_event_main_cta_block d-lg-none mb-3">
                            <t t-call="website_event.registration_template">
                                <t t-set="cta_additional_classes">w-100 py-sm-3 mt-sm-2</t>
                            </t>
                        </div>
                        <!-- Description -->
                        <div id="o_wevent_event_main_col">
                            <div t-if="edition_cover_only" class="css_non_editable_mode_hidden o_not_editable bg-info-light border border-info border-bottom-0 rounded-top p-2">
                                <em>Hidden for visitors</em>
                            </div>
                            <t t-if="not opt_event_description_cover_top" t-call="website.record_cover">
                                <t t-set="_record" t-value="event"/>
                                <t t-set="use_filters" t-value="True"/>
                                <t t-set="use_size" t-value="False"/>
                                <t t-set="use_text_align" t-value="True"/>
                                <t t-set="additionnal_classes" t-value="'o_wevent_event_main_cover overflow-hidden ' + ('css_non_editable_mode_hidden border border-info rounded-bottom' if edition_cover_only else 'd-none' if opt_event_description_cover_hidden else 'rounded')"/>

                                <div id="o_wevent_event_main_cover_content" class="p-3 mt-auto">
                                    <span t-if="event.is_participating" class="badge text-bg-success o_wevent_badge">
                                        <i class="fa fa-check me-2" role="img"/>Registered
                                    </span>
                                    <h2 t-field="event.name" itemprop="name" placeholder="Event Title" class="mt-3"/>
                                    <p class="lead" t-field="event.subtitle" placeholder="Event Subtitle"/>
                                </div>
                            </t>
                            <div class="o_wevent_event_main_dates_mobile d-lg-none mt-3">
                                <t t-call="website_event.event_description_dates"/>
                            </div>
                            <div class="mt-4" t-field="event.description" itemprop="description"/>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <aside id="o_wevent_event_main_sidebar" class="col-lg-4 my-3 mb-lg-0 d-print-none">
                        <!-- CTA (desktop only) -->
                        <div class="d-none d-lg-block mb-3">
                            <t t-call="website_event.registration_template">
                                <t t-set="cta_additional_classes">w-100</t>
                            </t>
                        </div>
                        <!-- Date & Time (desktop only) -->
                        <div class="d-none d-lg-block border-bottom pb-2 mb-3">
                            <meta itemprop="startDate" t-att-content="event.date_begin.isoformat()" />
                            <meta itemprop="endDate" t-att-content="event.date_end.isoformat()" />
                            <t t-call="website_event.event_description_dates"/>
                        </div>
                        <header class="d-lg-none mt-4 mb-2 py-3 border-top">
                            <h5 class="my-0">Event Info</h5>
                            <meta itemprop="eventStatus" content="https://schema.org/EventScheduled" />
                        </header>
                        <!-- Location -->
                        <meta itemprop="eventAttendanceMode" t-attf-content="https://schema.org/{{'Offline' if event.address_id else 'Online'}}EventAttendanceMode"/>
                        <div t-if="event.address_id" class="o_wevent_sidebar_block border-bottom pb-3 mb-4" itemprop="location" itemscope="itemscope" itemtype="https://schema.org/Place">
                            <h6 class="o_wevent_sidebar_title">Location</h6>
                            <div itemprop="name" class="mb-1" t-field="event.address_id"/>
                            <small class="d-block mb-2" t-field="event.address_id" t-options='{
                                "widget": "contact",
                                "fields": ["address"],
                                "no_marker": True,
                                "with_microdata": True,
                            }'/>
                            <small t-field="event.address_id" class="d-block mb-2" t-options='{
                                "widget": "contact",
                                "fields": ["phone", "mobile", "email"]
                            }'/>
                            <a t-att-href="event._google_map_link()" target="_blank" class="btn btn-light" title="Get directions">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="align-text-top" role="img" style="height: 1em; width: 1em">
                                    <rect width="24" height="24" opacity="0"/>
                                    <path role="presentation" fill="currentColor" d="M13.67 22h-.06a1 1 0 0 1-.92-.8l-1.54-7.57a1 1 0 0 0-.78-.78L2.8 11.31a1 1 0 0 1-.12-1.93l16-5.33A1 1 0 0 1 20 5.32l-5.33 16a1 1 0 0 1-1 .68z"/>
                                </svg>
                                Get directions
                            </a>
                        </div>
                        <div t-else="" class="o_wevent_sidebar_block border-bottom pb-3 mb-4">
                            <h6 class="o_wevent_sidebar_title">Location</h6>
                            <i class="fa fa-map-marker" title="Location"/> Online event
                        </div>
                        <!-- Organizer -->
                        <div t-if="event.organizer_id" class="o_wevent_sidebar_block border-bottom pb-3 mb-4" itemprop="organizer" itemscope="itemscope" itemtype="http://schema.org/Organization">
                            <h6 class="o_wevent_sidebar_title">Organizer</h6>
                            <div itemprop="name" class="mb-1" t-field="event.organizer_id"/>
                            <small t-field="event.organizer_id" t-options="{'widget': 'contact', 'fields': ['phone', 'mobile', 'email'], 'with_microdata': True,}"/>
                        </div>
                        <!-- Social -->
                        <div class="o_wevent_sidebar_block">
                            <h6 class="o_wevent_sidebar_title">Share</h6>
                            <p>Find out what people see and say about this event, and join the conversation.</p>
                            <t t-snippet-call="website.s_share">
                                <t t-set="_no_title" t-value="True"/>
                                <t t-set="_classes" t-valuef="o_wevent_sidebar_social mx-n1"/>
                                <t t-set="_link_classes" t-valuef="o_wevent_social_link"/>
                            </t>
                        </div>
                    </aside>
                </div>
            </div>
        </section>
    </t>
</template>

<!-- Event - Registration -->
<!-- cta_additional_classes {string} - classes applied to the main CTA   -->
<template id="registration_template" name="Registration">
    <button t-if="event.event_registrations_open" type="button" data-bs-toggle="modal" data-bs-target="#modal_ticket_registration" t-attf-class="btn btn-primary {{cta_additional_classes}}">Register</button>
    <div t-if="registration_error_code == 'insufficient_seats'" class="alert alert-danger my-3" role="alert">
        Registration failed! These tickets are not available anymore.
    </div>
    <t t-if="not event.event_registrations_open">
        <!-- Delayed registration time -->
        <div t-if="not event.event_registrations_started and not event.event_registrations_sold_out" class="alert alert-info mb-3 small" role="status">
            Registrations will open on
            <b t-out="event.start_sale_datetime" t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'format': 'MMMM dd, y'}"/>, at
            <b t-out="event.start_sale_datetime" t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'format': 'h:mm a'}"/>
            <small>(<t t-out="event.date_tz"/> time)</small>
        </div>
        <!-- Sold Out -->
        <div t-elif="event.event_registrations_sold_out" class="alert alert-danger mb-3 small" role="status">
            <b>Sold Out</b>
        </div>
        <!-- Registrations Closed -->
        <div t-else="" class="alert alert-warning mb-3 small" role="status">
            <em>Registrations <b>Closed</b></em>
        </div>
    </t>
</template>

<!-- Event - Description Dates -->
<template id="event_description_dates" name="Event Description Dates">
    <div t-attf-class="o_wevent_dates_block mb-3 {{event.is_one_day and 'd-flex flex-wrap justify-content-between align-items-center gap-2'}}">
        <div class="flex-wrap gap-3 w-lg-100">
            <div t-if="event.is_one_day" class="card bg-transparent d-inline-flex align-items-end flex-row flex-grow-1 w-lg-100 gap-2 p-2 p-md-3">
                <time t-field="event.date_begin" class="o_wevent_day_header_number lh-1" t-options="{'tz_name': event.date_tz, 'format': 'dd', 'date_only': 'true'}" t-att-datetime="event.date_begin"/>
                <div class="small">
                    <time t-field="event.date_begin" class="d-block lh-1" t-options="{'tz_name': event.date_tz, 'format': 'MMMM Y', 'date_only': 'true'}" t-att-datetime="event.date_begin"/>
                    <time t-out="event.date_begin"  t-att-datetime="event.date_begin" class="fw-bold lh-1" t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'time_only': 'true', 'format': 'short'}"/>
                    <i class="fa fa-long-arrow-right mx-1 text-muted" role="img"/>
                    <time t-out="event.date_end"  t-att-datetime="event.date_end" class="fw-bold lh-1" t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'time_only': 'true', 'format': 'short'}"/>
                </div>
            </div>

            <t t-else="">
                <div class="d-flex flex-lg-column flex-wrap gap-2 w-100">
                    <div class="flex-grow-1 w-lg-100">
                        <small class="fw-bold">Starts</small>
                        <div class="card bg-transparent d-flex align-items-end flex-row flex-grow-1 gap-2 py-3 px-2 px-md-3">
                            <time t-field="event.date_begin" class="o_wevent_day_header_number lh-1" t-options="{'tz_name': event.date_tz, 'format': 'dd', 'date_only': 'true'}" t-att-datetime="event.date_begin"/>
                            <div class="small">
                                <time t-field="event.date_begin" class="d-block lh-1" t-options="{'tz_name': event.date_tz, 'format': 'MMMM Y', 'date_only': 'true'}" t-att-datetime="event.date_begin"/>
                                <time t-out="event.date_begin"  t-att-datetime="event.date_begin" class="fw-bold lh-1" t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'time_only': 'true', 'format': 'short'}"/>
                            </div>
                        </div>
                    </div>
                    <div class="flex-grow-1 w-lg-100">
                        <small class="fw-bold">Ends</small>
                        <div class="card bg-transparent d-flex align-items-end flex-row flex-grow-1 gap-2 py-3 px-2 px-md-3">
                            <time t-field="event.date_end" class="o_wevent_day_header_number lh-1" t-options="{'tz_name': event.date_tz, 'format': 'dd', 'date_only': 'true'}" t-att-datetime="event.date_end"/>
                            <div class="small">
                                <time t-field="event.date_end" class="d-block lh-1" t-options="{'tz_name': event.date_tz, 'format': 'MMMM Y', 'date_only': 'true'}" t-att-datetime="event.date_end"/>
                                <time t-out="event.date_end"  t-att-datetime="event.date_end" class="fw-bold lh-1" t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'time_only': 'true', 'format': 'short'}"/>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </div>

        <div t-attf-class="d-flex align-items-center flex-wrap gap-2 {{'flex-basis-50 flex-basis-md-auto' if event.is_one_day else 'mt-3'}}">
            <small class="text-muted">Add to calendar:</small>
            <div t-attf-class="d-flex align-items-center flex-shrink-0 flex-grow-1 {{'gap-1 gap-md-2' if event.is_one_day else 'gap-2'}}">
                <a t-att-href="iCal_url" class="btn btn-light o_wevent_add_to_ical" title="Add to iCal">
                    <i role="img" class="fa fa-apple"/>
                </a>
                <a t-att-href="iCal_url" class="btn btn-light o_wevent_add_to_outlook" title="Add to Outlook">
                    <img src="/event/static/src/img/outlook-calendar.svg" alt="Outlook" loading="lazy"/>
                </a>
                <a t-att-href="google_url" class="btn btn-light o_wevent_add_to_google_calendar" title="Add to Google" target="_blank">
                    <img src="/event/static/src/img/google-calendar.svg" alt="Google Agenda" loading="lazy"/>
                </a>
            </div>
        </div>
    </div>
</template>


<template id="registration_attendee_details" name="Registration Attendee Details">
    <div id="modal_attendees_registration" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <form id="attendee_registration" t-attf-action="/event/#{slug(event)}/registration/confirm" method="post" class="js_website_submit_form">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                <div class="modal-content">
                    <div class="modal-header align-items-center">
                        <h4 class="modal-title">Attendees</h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"/>
                    </div>
                    <t t-set="counter" t-value="0"/>
                    <t t-set="input_type_by_question_type" t-value="{'name': 'text', 'email': 'email', 'phone': 'tel', 'company_name': 'text'}"/>
                    <t t-if="availability_check" t-foreach="tickets" t-as="ticket">
                        <t t-foreach="range(1, ticket['quantity'] + 1)" t-as="att_counter" name="attendee_loop">
                            <t t-set="counter" t-value="counter + 1"/>
                            <div class="modal-body">
                                <h5 t-attf-class="mt-1 pb-2 #{'border-bottom' if event.question_ids else ''}">Ticket #<span t-out="counter"/> <small class="text-muted">- <span t-out="ticket['name']"/></small></h5>
                                <div t-if="event.specific_question_ids" class="row">
                                    <t t-foreach="event.specific_question_ids" t-as="question">
                                        <div class="col-lg-6 mt-2">
                                            <t t-call="website_event.registration_event_question">
                                                <t t-set="registration_index" t-value="counter"/>
                                            </t>
                                        </div>
                                    </t>
                                </div>
                                <input class="d-none" type="text" t-attf-name="#{counter}-event_ticket_id" t-attf-value="#{ticket['id']}"/>
                            </div>
                        </t>
                    </t>
                    <div t-if="availability_check and event.general_question_ids" class="modal-body border-top o_wevent_registration_question_global">
                        <div class="row">
                            <t t-foreach="event.general_question_ids" t-as="question">
                                <div class="mt-2" t-att-class="question.question_type=='text_box' and 'col-lg-12' or 'col-lg-6'">
                                    <t t-call="website_event.registration_event_question">
                                        <t t-set="registration_index" t-value="0"/>
                                    </t>
                                </div>
                            </t>
                        </div>
                    </div>
                    <t t-elif="not availability_check">
                        <div class="modal-body border-bottom">
                            <strong> You ordered more tickets than available seats</strong>
                        </div>
                    </t>
                    <div class="modal-footer border-top">
                        <button type="button" class="btn btn-secondary js_goto_event" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" t-if="availability_check">Confirm Registration</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</template>

<template id="registration_event_question" name="Registration Event Question">
    <label t-out="question.title"/>
    <span t-if="question.is_mandatory_answer">*</span>
    <t t-if="question.question_type in ['name', 'email', 'phone', 'company_name']">
        <input class="form-control" t-att-type="input_type_by_question_type[question.question_type]"
               t-attf-name="#{registration_index}-#{question.question_type}-#{question.id}"
               t-att-required="question.is_mandatory_answer" t-att-value="default_first_attendee.get(question.question_type, '') if counter in (0,1) else ''"/>
    </t>
    <t t-elif="question.question_type == 'simple_choice'">
        <select t-attf-name="#{registration_index}-#{question.question_type}-#{question.id}"
                class="form-select" t-att-required="question.is_mandatory_answer">
            <option value=""/>
            <t t-foreach="question.answer_ids" t-as="answer">
                <option t-out="answer.name" t-att-value="answer.id"/>
            </t>
        </select>
    </t>
    <t t-else="">
        <textarea t-attf-name="#{registration_index}-#{question.question_type}-#{question.id}"
                  class="col-lg-12 form-control" t-att-required="question.is_mandatory_answer"/>
    </t>
</template>

<template id="registration_complete" name="Registration Completed">
    <t t-call="website_event.layout">
        <div class="container my-5 o_wereg_confirmed">
            <div class="row mb-3">
                <div class="col-12">
                    <h3>Registration confirmed!</h3>
                    <u><a class="h4 text-primary" t-out="event.name" t-att-href="event.website_url" /></u>
                </div>
            </div>
            <div class="d-flex flex-column justify-content-between flex-lg-row">
                <div>
                    <t t-if="attendees" t-call="website_event.registration_ticket_access"/>
                    <div class="mt-4">
                        <h5>Don't miss out!</h5>
                        <small>Add this event to your calendar</small>
                        <div id="add_to_calendar" class="o_event_add_to_calendar_btns d-flex flex-wrap gap-3 mt-2">
                            <a role="button" class="o_outlook_calendar btn btn-block bg-white" t-att-href="iCal_url">
                                <img src="/event/static/src/img/outlook-calendar.svg" alt="Outlook" loading="lazy"/>
                                Add to iCal/Outlook
                            </a>
                            <a role="button" class="o_google_calendar btn btn-block bg-white" t-att-href="google_url">
                                <img src="/event/static/src/img/google-calendar.svg" alt="Google Agenda" loading="lazy"/>
                                Add to Google Agenda
                            </a>
                        </div>
                    </div>
                </div>
                <div class="mt-4 mt-lg-0">
                    <h5>Event Details</h5>
                    <div class="row">
                        <div class="col-auto">
                            <b>Start</b>
                        </div>
                        <div class="col-auto ps-0">
                            <span t-out="event.date_begin_located"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-auto">
                            <b>End</b>
                        </div>
                        <div class="col-auto ps-0">
                            <span t-out="event.date_end_located"/>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h5 t-field="event.address_id" class="text-secondary fw-bold" t-options='{
                            "widget": "contact",
                            "fields": ["name"]
                            }'/>
                        <a t-att-href="event.google_map_link()" target="_BLANK" t-field="event.address_id" t-options='{
                            "widget": "contact",
                            "fields": ["address"]
                            }'/>
                        <div t-field="event.organizer_id" t-options='{
                            "widget": "contact",
                            "fields": ["phone", "mobile", "email"]
                            }'/>
                    </div>
                </div>
            </div>
        </div>
        <input t-if='website.plausible_shared_key' type='hidden' class='js_plausible_push' data-event-name='Lead Generation' t-attf-data-event-params='{"CTA": "Event Registration"}' />
    </t>
</template>

<template id="registration_ticket_access" name="Registration Ticket Access">
    <div class="row mb-3 o_wevent_registration_ticket_access">
        <div class="col-12 mb-2 d-flex flex-column flex-md-row align-items-stretch align-items-md-baseline justify-content-md-start">
            <a class="btn btn-primary o_wevent_download_ticket_btn" title="Download All Tickets" target="_blank"
                t-attf-href="/event/{{ event.id }}/my_tickets?registration_ids={{ attendees.ids }}&amp;tickets_hash={{ event._get_tickets_access_hash(attendees.ids) }}">
                Download Tickets <i class="ms-1 fa fa-download"/>
            </a>
        </div>
    </div>
</template>

<!-- Button to configure Tickets -->
<template id="registration_configure_tickets_button" name="Registration Configure Ticket Button">
    <a t-attf-class="o_not_editable text-nowrap {{linkClasses or '' }}" t-attf-href="/odoo/event.event/{{event.id}}?menu_id={{backend_menu_id}}" role="link"  title="Configure event tickets">
        <i class="fa fa-gear me-1" role="img" aria-label="Configure" title="Configure event tickets"/><em>Configure Tickets</em>
    </a>
</template>

<template id="modal_ticket_registration" name="Modal for tickets registration">
    <!-- Modal -->
    <div class="modal fade" id="modal_ticket_registration" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
            <div class="modal-header">
                <div class="o_wevent_registration_title modal-title fs-5" id="staticBackdropLabel">Tickets</div>
                <div t-if="len(event.event_ticket_ids) &gt; 2" class="o_wevent_price_range ms-2"/>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <form t-if="event.event_registrations_open and (not event.event_ticket_ids or any(not ticket.is_expired for ticket in event.event_ticket_ids))"
                id="registration_form"
                t-attf-action="/event/#{slug(event)}/registration/new" method="post"
                itemscope="itemscope" itemprop="offers" itemtype="http://schema.org/AggregateOffer">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                <div id="o_wevent_tickets" class="shadow-sm o_wevent_js_ticket_details">
                    <div t-if="registration_error_code == 'insufficient_seats'" class="alert alert-danger" role="alert">
                        <p class="mb-0">
                            Registration failed! These tickets are not available anymore.
                        </p>
                    </div>
                    <t t-set="tickets" t-value="event.event_ticket_ids.filtered(lambda ticket: not ticket.is_expired)"/>
                    <!-- If some tickets expired and there is only one type left, we keep the same layout -->
                    <t t-if="len(event.event_ticket_ids) &gt; 1">
                        <span t-if="not event.event_registrations_open" class="text-danger">
                            <i class="fa fa-ban me-2"/>Sold Out
                        </span>
                        <div id="o_wevent_tickets_collapse" class="modal-body collapse show">
                            <div t-foreach="tickets" t-as="ticket"
                                t-attf-class="d-flex justify-content-between o_wevent_ticket_selector mb-2 pb-2 {{not ticket_last and 'border-bottom' or ''}}"
                                t-att-name="ticket.name">
                                <div itemscope="itemscope" itemtype="http://schema.org/Offer">
                                    <h5 itemprop="name" t-field="ticket.name" class="h6 my-0"/>
                                    <t t-if="ticket.description">
                                        <small t-field="ticket.description" class="text-muted py-2"/>
                                        <br/>
                                    </t>
                                    <div class="d-flex align-items-center me-3">
                    <label for="initial_deposit" class="form-label me-2 mb-0">Initial Deposit ($):</label>
                    <input type="number"
                           class="form-control"
                           id="initial_deposit"
                           name="initial_deposit"
                           min="0"
                           step="0.01"
                           placeholder="0.00"
                           style="width: 120px;"/>
                </div>
                                    <small t-if="ticket.end_sale_datetime and ticket.sale_available and not ticket.is_expired"
                                        class="text-muted me-3" itemprop="availabilityEnds">Sales end on
                                        <span itemprop="priceValidUntil" t-out="ticket.end_sale_datetime"
                                        t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'format': 'short'}"/>
                                        (<span t-out="ticket.event_id.date_tz"/>)
                                    </small>
                                    <small t-if="ticket.start_sale_datetime and not ticket.sale_available and not ticket.is_expired"
                                        class="text-muted me-3" itemprop="availabilityEnds">
                                        Sales start on <span itemprop="priceValidUntil" t-out="ticket.start_sale_datetime"
                                        t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'format': 'short'}"/>
                                        (<span t-out="ticket.event_id.date_tz"/>)
                                    </small>
                                </div>
                                <div class="d-flex flex-column flex-md-row align-items-center justify-content-between gap-2">
                                    <div class="o_wevent_registration_multi_select flex-md-grow-1 text-end"/>
                                    <div class="ms-auto">
                                        <select t-if="not ticket.is_expired and ticket.sale_available"
                                            t-attf-name="nb_register-#{ticket.id}"
                                            class="w-auto form-select">
                                            <t t-set="seats_max_ticket" t-value="(not ticket.seats_limited or ticket.seats_available &gt; 9) and 10 or ticket.seats_available + 1"/>
                                            <t t-set="seats_max_event" t-value="(not event.seats_limited or event.seats_available &gt; 9) and 10 or event.seats_available + 1"/>
                                            <t t-set="seats_max" t-value="min(seats_max_ticket, seats_max_event)"/>
                                            <t t-foreach="range(0, seats_max)" t-as="nb">
                                                <option t-out="nb" t-att-selected="len(ticket) == 0 and nb == 0 and 'selected'"/>
                                            </t>
                                        </select>
                                        <div t-else="" class="text-danger">
                                            <span t-if="not ticket.sale_available and not ticket.is_expired and ticket.is_launched" >Sold Out</span>
                                            <span t-if="ticket.is_expired">Expired</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer flex-lg-row border-top">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary a-submit" disabled="" t-attf-id="#{event.id}">
                                Register
                                <t t-if="event.seats_limited and event.seats_max and event.seats_available &lt;= (event.seats_max * 0.2)">
                                    (only <t t-out="event.seats_available"/> available)
                                </t>
                            </button>
                        </div>
                    </t>
                    <div t-else="" class="o_wevent_registration_single">
                        <div class="modal-body row px-3 py-2 mx-0">
                            <div class="col-12 col-md-8 p-0" itemscope="itemscope" itemtype="http://schema.org/Offer">
                                <h5 itemprop="name" class="my-0 pe-3 o_wevent_single_ticket_name">
                                    <span t-if="tickets" t-field="tickets.name"/>
                                    <span t-else="">Registration</span>
                                </h5>
                                <t t-if="tickets.description">
                                    <small t-field="tickets.description" class="text-muted py-2"/>
                                    <br/>
                                </t>
                                <small t-if="tickets.end_sale_datetime and tickets.sale_available and not tickets.is_expired"
                                    class="text-muted" itemprop="availabilityEnds">
                                    Sales end on
                                    <span itemprop="priceValidUntil" t-out="tickets.end_sale_datetime"
                                        t-options="{'widget': 'datetime', 'tz_name': event.date_tz, 'format': 'short'}"/>
                                    (<span t-out="tickets.event_id.date_tz"/>)
                                </small>
                            </div>
                            <div class="col-md-4 d-flex align-items-center justify-content-between p-0">
                                <t t-if="event.event_registrations_open">
                                    <link itemprop="availability" content="http://schema.org/InStock"/>
                                    <div class="o_wevent_registration_single_select w-auto ms-auto">
                                        <select t-att-name="'nb_register-%s' % (tickets.id if tickets else 0)" class="d-inline w-auto form-select">
                                            <t t-set="seats_max_ticket" t-value="(not tickets or not tickets.seats_limited or tickets.seats_available &gt; 9) and 10 or tickets.seats_available + 1"/>
                                            <t t-set="seats_max_event" t-value="(not event.seats_limited or event.seats_available &gt; 9) and 10 or event.seats_available + 1"/>
                                            <t t-set="seats_max" t-value="min(seats_max_ticket, seats_max_event) if tickets else seats_max_event"/>
                                            <t t-foreach="range(0, seats_max)" t-as="nb">
                                                <option t-out="nb" t-att-selected="nb == 1 and 'selected'"/>
                                            </t>
                                        </select>
                                    </div>
                                </t>
                                <t t-else="">
                                    <span itemprop="availability" content="http://schema.org/SoldOut" class="text-danger">
                                        <i class="fa fa-ban me-2"/>Sold Out
                                    </span>
                                </t>
                            </div>
                        </div>
                        <div class="modal-footer flex-lg-row border-top">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary a-submit" t-attf-id="#{event.id}" disabled="disabled">
                                Register
                                <t t-if="event.seats_limited and event.seats_max and event.seats_available &lt;= (event.seats_max * 0.2)">
                                    (only <t t-out="event.seats_available"/> available)
                                </t>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</template>


<!-- Cover Options -->
<template id="opt_event_description_cover_hidden" name="Event Description Cover Hidden Option" inherit_id="website_event.event_description_full" active="False"/>
<template id="opt_event_description_cover_top" name="Event Description Cover Top Option" inherit_id="website_event.event_description_full" active="False"/>

</odoo>
