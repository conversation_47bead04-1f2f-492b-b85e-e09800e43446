<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="action_report_payslip_hk" model="ir.actions.report">
        <field name="name">HK: Payslip</field>
        <field name="model">hr.payslip</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">l10n_hk_hr_payroll.report_payslip</field>
        <field name="report_file">l10n_hk_hr_payroll.report_payslip</field>
        <field name="print_report_name">'Payslip - %s' % (object.employee_id.name)</field>
        <field name="binding_model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_type">report</field>
        <!-- Erase fields to avoid double PDF posting -->
        <field name="attachment"></field>
        <field name="attachment_use" eval="False"/>
    </record>

    <record id="action_report_light_payslip_hk" model="ir.actions.report">
        <field name="name">HK: Payslip (Light)</field>
        <field name="model">hr.payslip</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">l10n_hk_hr_payroll.report_light_payslip</field>
        <field name="report_file">l10n_hk_hr_payroll.report_light_payslip</field>
        <field name="print_report_name">'Payslip - %s' % (object.employee_id.name)</field>
        <field name="binding_model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_type">report</field>
        <!-- Erase fields to avoid double PDF posting -->
        <field name="attachment"></field>
        <field name="attachment_use" eval="False"/>
    </record>
</odoo>
