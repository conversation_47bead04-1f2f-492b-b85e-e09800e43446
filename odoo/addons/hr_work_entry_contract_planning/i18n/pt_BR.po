# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_planning
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,help:hr_work_entry_contract_planning.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"Define a origem para a geração de registros de trabalho\n"
"\n"
"Horário de trabalho: os registros de trabalho serão gerados a partir das horas de trabalho abaixo.\n"
"\n"
"Presença: os registros de trabalho serão gerados a partir das presenças do funcionário (requer o aplicativo Controle de Presença).\n"
"Planejamento: os registros de trabalho serão gerados a partir do planejamento do funcionário (requer o aplicativo Planejamento)."

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_contract
msgid "Employee Contract"
msgstr "Contrato do funcionário"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_work_entry
msgid "HR Work Entry"
msgstr "RH - Registro de trabalho"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields.selection,name:hr_work_entry_contract_planning.selection__hr_contract__work_entry_source__planning
msgid "Planning"
msgstr "Planejamento"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_planning_slot
msgid "Planning Shift"
msgstr "Planejamento de escala"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_hr_work_entry__planning_slot_id
msgid "Planning Slot"
msgstr "Faixa de horário de planejamento"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "Gerar novamente registros de trabalho do funcionário"

#. module: hr_work_entry_contract_planning
#. odoo-python
#: code:addons/hr_work_entry_contract_planning/models/planning_slot.py:0
msgid ""
"This shift record is linked to a validated working entry. You can't delete "
"it."
msgstr ""
"Esse registro de turno está vinculado a um registro de trabalho validada. "
"Não é possível excluí-lo."

#. module: hr_work_entry_contract_planning
#. odoo-python
#: code:addons/hr_work_entry_contract_planning/models/planning_slot.py:0
msgid ""
"This shift record is linked to a validated working entry. You can't modify "
"it."
msgstr ""
"Esse registro de turno está vinculado a um registro de trabalho validada. "
"Não é possível modificá-lo."

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr "Origem do registro de trabalho"
