# -*- coding: utf-8 -*-

import logging

from dateutil.relativedelta import relativedelta

from odoo import models, api, fields, Command, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    deposit_order_id = fields.Many2one('sale.order', string="Deposit Order", index=True)

    @api.depends('order_line', 'order_line.recurring_invoice')
    def _compute_has_recurring_line(self):
        recurring_product_orders = self.order_line.filtered(lambda l: l.product_id.recurring_invoice and not l.subscription_deposit_value).order_id
        recurring_product_orders.has_recurring_line = True
        (self - recurring_product_orders).has_recurring_line = False

    def _cart_update_order_line(self, product_id, quantity, order_line, **kwargs):
        product = self.env['product.product'].browse(product_id)
        selected_plan_id = None

        _logger.info("Cart update kwargs: %s", kwargs)
        _logger.info("Product recurring_invoice: %s", product.recurring_invoice)
        _logger.info("Deposit value: %s", kwargs.get('deposit_value', 0))

        if product.recurring_invoice and kwargs.get('deposit_value', 0) > 0:
            selected_plan_id = kwargs.get('plan_id')
            _logger.info("Captured plan_id from kwargs: %s", selected_plan_id)

            # Validate minimum deposit
            min_deposit = product.initial_deposit_value
            deposit_value = float(kwargs.get('deposit_value', 0))
            if product.initial_deposit_type == 'percentage':
                if deposit_value < min_deposit:
                    raise UserError(_('Deposit percentage must be at least %s%%') % min_deposit)
            else:
                if deposit_value < min_deposit:
                    raise UserError(_('Deposit amount must be at least $%s') % min_deposit)

        order_line = super()._cart_update_order_line(product_id, quantity, order_line, **kwargs)

        _logger.info("After super call - order plan_id: %s", self.plan_id.id if self.plan_id else None)

        if product.recurring_invoice and kwargs.get('deposit_value', 0) > 0:
            if selected_plan_id:
                order_line.selected_plan_id = int(selected_plan_id)
                _logger.info("Stored selected_plan_id %s on order line %s", selected_plan_id, order_line.id)
            elif self.plan_id:
                order_line.selected_plan_id = self.plan_id.id
                _logger.info("Stored order plan_id %s on order line %s", self.plan_id.id, order_line.id)

            self.plan_id = False

            if order_line:
                order_line.subscription_deposit_type = kwargs.get('deposit_type', False)
                order_line.subscription_deposit_value = kwargs.get('deposit_value')
                if selected_plan_id:
                    order_line.selected_plan_id = int(selected_plan_id)
                elif self.plan_id:
                    order_line.selected_plan_id = self.plan_id.id
                _logger.info("Updated existing line with plan info")

        return order_line

    def _prepare_order_line_values(self, product_id, quantity, deposit_type=False, deposit_value=False, **kwargs):
        values = super()._prepare_order_line_values(product_id, quantity, **kwargs)
        product = self.env['product.product'].browse(product_id)
        if product.recurring_invoice and deposit_value and deposit_value > 0:
            # Validate minimum deposit
            min_deposit = product.initial_deposit_value
            deposit_value = float(deposit_value)
            if product.initial_deposit_type == 'percentage':
                if deposit_value < min_deposit:
                    raise UserError(_('Deposit percentage must be at least %s%%') % min_deposit)
            else:
                if deposit_value < min_deposit:
                    raise UserError(_('Deposit amount must be at least $%s') % min_deposit)

            values['subscription_deposit_type'] = deposit_type
            values['subscription_deposit_value'] = deposit_value
            if kwargs.get('plan_id'):
                values['selected_plan_id'] = int(kwargs.get('plan_id'))
                _logger.info("Prepared order line values with selected_plan_id: %s", kwargs.get('plan_id'))
        return values

    def action_confirm(self):
        # implement installment for remaining
        res = super(SaleOrder, self).action_confirm()
        for so in self:
            so._process_subscription_deposits()
        return res

    def _process_subscription_deposits(self):
        """Process subscription products with initial deposits"""
        deposit_lines = self.order_line.filtered(
            lambda l: l.product_id.recurring_invoice and
                     l.product_id.allow_initial_deposit and
                     l.subscription_deposit_value > 0 and
                     l.subscription_deposit_type
        )
        if not deposit_lines:
            return
        for line in deposit_lines:
            self._create_deposit_and_subscription_orders(line)

    def _create_deposit_and_subscription_orders(self, line):
        """Create separate orders for deposit and subscription"""
        original_unit_price = line.product_id.list_price
        original_total_price = original_unit_price * line.product_uom_qty

        deposit_amount = self._calculate_deposit_amount_from_original_price(line, original_total_price)
        remaining_amount = original_total_price - deposit_amount

        _logger.info(
            "Processing subscription deposit for line %s: "
            "Original price: %s, Deposit: %s (%s), Remaining: %s",
            line.product_id.name, original_total_price, deposit_amount,
            line.subscription_deposit_type, remaining_amount
        )

        if remaining_amount <= 0:
            _logger.info("Deposit covers full amount, no subscription order needed")
            return

        subscription_order = self._create_subscription_order(line, remaining_amount)
        self._update_line_for_deposit(line, deposit_amount)

        _logger.info("Created subscription order %s for remaining amount %s",
                    subscription_order.name, remaining_amount)

        return subscription_order

    def _calculate_deposit_amount(self, line):
        """Calculate the deposit amount based on type (legacy method)"""
        if line.subscription_deposit_type == 'usd':
            return min(line.subscription_deposit_value, line.price_subtotal)
        elif line.subscription_deposit_type == 'percentage':
            percentage = min(line.subscription_deposit_value, 100.0)
            return line.price_subtotal * (percentage / 100.0)
        return 0.0

    def _calculate_deposit_amount_from_original_price(self, line, original_total_price):
        """Calculate the deposit amount based on original product price"""
        if line.subscription_deposit_type == 'usd':
            return min(line.subscription_deposit_value, original_total_price)
        elif line.subscription_deposit_type == 'percentage':
            percentage = min(line.subscription_deposit_value, 100.0)
            return original_total_price * (percentage / 100.0)
        return 0.0

    def _create_subscription_order(self, line, remaining_amount):
        """Create a subscription order for the remaining amount"""
        plan_id = self._get_subscription_plan(line)

        if not plan_id:
            raise UserError(_('No subscription plan found for product %s. Please configure a subscription plan.') % line.product_id.name)

        duration = plan_id.total_duration or 1
        new_unit_price = (remaining_amount / line.product_uom_qty) / duration

        next_invoice_date = self.date_order.date() + relativedelta(months=1)
        end_date = next_invoice_date + relativedelta(months=duration) + relativedelta(days=1)
        deposit_order = line.order_id

        subscription_vals = {
            'partner_id': self.partner_id.id,
            'partner_invoice_id': self.partner_invoice_id.id,
            'partner_shipping_id': self.partner_shipping_id.id,
            'pricelist_id': self.pricelist_id.id,
            'currency_id': self.currency_id.id,
            'company_id': self.company_id.id,
            'plan_id': plan_id.id,
            'subscription_state': '1_draft',
            'is_subscription': True,
            'origin': self.name,
            'next_invoice_date': next_invoice_date,
            'end_date': end_date,
            'deposit_order_id': deposit_order.id
        }

        if deposit_order._check_token_saving_conditions():
            pay_token = deposit_order.transaction_ids.sudo()._get_last().token_id.id
            if pay_token:
                subscription_vals['payment_token_id'] = pay_token
            _logger.info("Copied payment token %s to subscription order", pay_token)

        subscription_order = self.env['sale.order'].create(subscription_vals)

        line_vals = {
            'order_id': subscription_order.id,
            'product_id': line.product_id.id,
            'product_uom_qty': line.product_uom_qty,
            'product_uom': line.product_uom.id,
            'price_unit': new_unit_price,
            'name': line.name + _(' (Subscription)'),
            'tax_id': [Command.set(line.tax_id.ids)],
            'custom_subscription_price': True
        }

        subscription_line = self.env['sale.order.line'].create(line_vals)
        if self.state == 'sale':
            subscription_order.action_confirm()

        return subscription_order

    def _get_subscription_plan(self, line):
        """Get subscription plan for the product line"""
        _logger.info("Getting subscription plan for line %s (product: %s)", line.id, line.product_id.name)
        _logger.info("Line selected_plan_id: %s", line.selected_plan_id.id if line.selected_plan_id else None)
        _logger.info("Order plan_id: %s", self.plan_id.id if self.plan_id else None)

        if line.selected_plan_id:
            _logger.info("Using line selected_plan_id: %s", line.selected_plan_id.name)
            return line.selected_plan_id

        if self.plan_id:
            _logger.info("Using order plan_id: %s", self.plan_id.name)
            return self.plan_id

        if hasattr(line.product_id, 'subscription_plan_id') and line.product_id.subscription_plan_id:
            _logger.info("Using product subscription_plan_id: %s", line.product_id.subscription_plan_id.name)
            return line.product_id.subscription_plan_id

        plan = self.env['sale.subscription.plan'].search([
            ('billing_period_value', '=', 1),
            ('billing_period_unit', '=', 'month')
        ], limit=1)

        if not plan:
            plan = self.env['sale.subscription.plan'].create({
                'name': 'Monthly Plan',
                'billing_period_value': 1,
                'billing_period_unit': 'month',
            })

        _logger.info("Using default/created plan: %s", plan.name)
        return plan

    def _update_line_for_deposit(self, line, deposit_amount):
        """Update the order line to reflect only the deposit amount"""
        new_unit_price = deposit_amount / line.product_uom_qty

        line.write({
            'price_unit': new_unit_price,
            'name': line.name + _(' (Initial Deposit)'),
        })

        # line.write({
        #     'subscription_deposit_type': False,
        #     'subscription_deposit_value': 0.0,
        # })
